{"version": 3, "file": "responses.js", "sourceRoot": "", "sources": ["../../../src/cmap/wire_protocol/responses.ts"], "names": [], "mappings": ";;;AAyCA,0CAwBC;AAjED,qCAYoB;AACpB,uCAAsF;AAEtF,uCAA2D;AAC3D,mDAI8B;AAE9B,MAAM,iBAAiB,GAAG;IACxB,IAAI,EAAE,CAAC;IACP,UAAU,EAAE,CAAC;IACb,UAAU,EAAE,CAAC;IACb,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;CACD,CAAC;AAEX;;;;;;;;;;GAUG;AACH,SAAgB,eAAe,CAAC,IAAgB,EAAE,QAAuB;IACvE,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;QAClD,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAE/B,IAAI,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YAChD,MAAM,UAAU,GAAG,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAEzD,yBAAyB;YACzB,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;gBAC7D,MAAM,WAAW,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;gBACtD,MAAM,WAAW,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;gBAEtD,+EAA+E;gBAC/E,gCAAgC;gBAChC,KAAK,IAAI,CAAC,GAAG,WAAW,EAAE,CAAC,GAAG,WAAW,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC7D,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI;wBAAE,OAAO,KAAK,CAAC;gBACrC,CAAC;gBAED,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAQD,gBAAgB;AAChB,MAAa,eAAgB,SAAQ,2BAAgB;IAYnC,GAAG,CACjB,IAAqB,EACrB,EAAK,EACL,QAAkB;QAElB,IAAI,CAAC;YACH,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;QACvC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,0CAAkC,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED,MAAM,CAAC,EAAE,CAAC,KAAc;QACtB,OAAO,KAAK,YAAY,eAAe,CAAC;IAC1C,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,IAAgB;QAC1B,MAAM,QAAQ,GAAG,IAAA,6BAAsB,EAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACjD,MAAM,OAAO,GAAG,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAChD,OAAO,OAAO;YACZ,CAAC,CAAC,IAAI,eAAe,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC;YAC/C,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;IACzC,CAAC;IAKD;;;;;OAKG;IACH,IAAI,qBAAqB;QACvB,yBAAyB;QACzB,MAAM,UAAU,GAAG,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,2BAAmB,CAAC,gBAAgB,CAAC;QACvF,IAAI,UAAU;YAAE,OAAO,IAAI,CAAC;QAE5B,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QAEhC,8CAA8C;QAC9C,MAAM,cAAc,GAClB,IAAI,CAAC,GAAG,CAAC,mBAAmB,EAAE,eAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC;YACjE,2BAAmB,CAAC,gBAAgB,CAAC;QACvC,IAAI,cAAc;YAAE,OAAO,IAAI,CAAC;QAEhC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,eAAQ,CAAC,KAAK,CAAC,CAAC;QAC5D,IAAI,WAAW,EAAE,IAAI,EAAE,EAAE,CAAC;YACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5C,MAAM,YAAY,GAChB,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,eAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC;oBACtD,2BAAmB,CAAC,gBAAgB,CAAC;gBAEvC,0CAA0C;gBAC1C,IAAI,YAAY;oBAAE,OAAO,IAAI,CAAC;YAChC,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,IAAI,aAAa;QACf,OAAO,CACL,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,eAAQ,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC;YACnD,aAAa,EAAE,KAAK;YACpB,YAAY,EAAE,KAAK;YACnB,cAAc,EAAE,KAAK;YACrB,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;SAC3B,CAAC,IAAI,IAAI,CACX,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,IAAW,aAAa;QACtB,OAAO,CACL,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAQ,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,eAAe,EAAE,eAAQ,CAAC,SAAS,CAAC;YAC7E,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,eAAQ,CAAC,SAAS,CAAC,CAC9C,CAAC;IACJ,CAAC;IAED,IAAW,aAAa;QACtB,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,eAAQ,CAAC,SAAS,CAAC,CAAC;IACvD,CAAC;IAED,oEAAoE;IACpE,IAAW,EAAE;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC;IAED,IAAW,IAAI;QACb,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,eAAQ,CAAC,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED,IAAW,MAAM;QACf,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAQ,CAAC,MAAM,CAAC,CAAC;IAC7C,CAAC;IAED,IAAW,IAAI;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAGD,IAAW,YAAY;QACrB,IAAI,CAAC,CAAC,aAAa,IAAI,IAAI,CAAC,EAAE,CAAC;YAC7B,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,eAAQ,CAAC,MAAM,CAAC,CAAC;YACjE,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;gBAC3B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAM,WAAW,GAAG,cAAc,CAAC,GAAG,CAAC,aAAa,EAAE,eAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAChF,MAAM,SAAS,GAAG,cAAc,CAAC,GAAG,CAAC,WAAW,EAAE,eAAQ,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,CAAC;YAC/E,wEAAwE;YACxE,IAAI,CAAC,WAAW,GAAG,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC;QAChD,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC;IAClC,CAAC;IAEe,QAAQ,CAAC,OAA8B;QACrD,MAAM,gBAAgB,GAAG;YACvB,GAAG,IAAA,gCAAyB,EAAC,OAAO,IAAI,EAAE,CAAC;YAC3C,UAAU,EAAE,IAAA,gCAAyB,EAAC,OAAO,CAAC;SAC/C,CAAC;QACF,OAAO,KAAK,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;IAC1C,CAAC;;AA/IH,0CAgJC;AA5GC,SAAS;AACF,qBAAK,GAAG,IAAI,eAAe,CAAC,IAAI,UAAU,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AA6GpG,gBAAgB;AAChB,MAAa,cAAe,SAAQ,eAAe;IAAnD;;QAoBU,WAAM,GAA4B,IAAI,CAAC;QACvC,aAAQ,GAAG,CAAC,CAAC;QAwBb,oBAAe,GAA4B,IAAI,CAAC;IA+D1D,CAAC;IApGC;;;OAGG;IACH,MAAM,KAAK,YAAY;QACrB,OAAO,IAAI,cAAc,CAAC,IAAA,gBAAS,EAAC,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IACrF,CAAC;IAED,MAAM,CAAU,EAAE,CAAC,KAAc;QAC/B,OAAO,KAAK,YAAY,cAAc,IAAI,KAAK,KAAK,cAAc,CAAC,YAAY,CAAC;IAClF,CAAC;IAKD,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACnD,CAAC;IAED,IAAW,EAAE;QACX,IAAI,CAAC;YACH,OAAO,WAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,eAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,0CAAkC,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED,IAAW,EAAE;QACX,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,eAAQ,CAAC,MAAM,CAAC,CAAC;QACzD,IAAI,SAAS,IAAI,IAAI;YAAE,OAAO,IAAA,UAAE,EAAC,SAAS,CAAC,CAAC;QAC5C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAW,MAAM;QACf,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IACrD,CAAC;IAGD,IAAI,cAAc;QAChB,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI;YAAE,OAAO,IAAI,CAAC;QAChD,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI;YAAE,OAAO,IAAI,CAAC,eAAe,CAAC;QAE9D,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,GAAG,CAAC,QAAQ,EAAE,eAAQ,CAAC,MAAM,CAAC,CAAC;QACtE,IAAI,MAAM,EAAE,GAAG,CAAC,YAAY,CAAC;YAC3B,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,eAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;aACnE,IAAI,MAAM,EAAE,GAAG,CAAC,WAAW,CAAC;YAC/B,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,eAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;;YAClE,MAAM,IAAI,0CAAkC,CAAC,yCAAyC,CAAC,CAAC;QAE7F,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED,IAAY,KAAK;QACf,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI;YAAE,OAAO,IAAI,CAAC,MAAM,CAAC;QAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC;YAAE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,eAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;aACtF,IAAI,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC;YAAE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,eAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;;YACzF,MAAM,IAAI,0CAAkC,CAAC,yCAAyC,CAAC,CAAC;QAC7F,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,IAAW,SAAS;QAClB,OAAO,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;IAC5B,CAAC;IAED,IAAW,oBAAoB;QAC7B,OAAO,CACL,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,EAAE,eAAQ,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC;YACjE,aAAa,EAAE,KAAK;YACpB,YAAY,EAAE,KAAK;YACnB,cAAc,EAAE,KAAK;YACrB,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;SAC3B,CAAC,IAAI,IAAI,CACX,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,OAA2C;QACtD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,eAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC;QAC5E,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,eAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC;QAE/F,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;QAEnB,IAAI,OAAO,EAAE,GAAG,EAAE,CAAC;YACjB,OAAO,MAAM,CAAC,OAAO,EAAE,CAAC;QAC1B,CAAC;aAAM,CAAC;YACN,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACxC,IAAI,eAAe,EAAE,CAAC;gBACpB,IAAA,gCAAwB,EAAC,MAAM,EAAE,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC;YAC5E,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAEM,KAAK;QACV,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;IACjC,CAAC;CACF;AA5GD,wCA4GC;AAED;;;;;GAKG;AACH,MAAa,uBAAwB,SAAQ,cAAc;IAA3D;;QACE,cAAS,GAAG,IAAI,CAAC;QAcjB,YAAO,GAAG,CAAC,CAAC;IAUd,CAAC;IAtBC,IAAa,EAAE;QACb,OAAO,WAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;IAC7B,CAAC;IAED,IAAa,SAAS;QACpB,OAAO,CAAC,CAAC;IACX,CAAC;IAED,IAAa,EAAE;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAGD,IAAa,MAAM;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEQ,KAAK,CAAC,OAA4B;QACzC,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QACpC,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;CACF;AAzBD,0DAyBC;AAED;;;GAGG;AACH,MAAa,6BAA8B,SAAQ,cAAc;IAC/D,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,eAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IACnD,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,eAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IACnD,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,eAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAClD,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,eAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IACnD,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,eAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAClD,CAAC;IAED,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,GAAG,CAAC,mBAAmB,EAAE,eAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC/D,CAAC;CACF;AAxBD,sEAwBC"}